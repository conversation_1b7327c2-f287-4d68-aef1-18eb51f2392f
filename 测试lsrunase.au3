#include <MsgBoxConstants.au3>

; 测试lsrunase功能的简单脚本
; 用于验证lsrunase是否能正确以管理员权限运行程序

; 配置参数
Local $sUser = "administrator"
Local $sPassword = "40lkg/kN/6Pm3ffhFw=="
Local $sDomain = "nvtpower.com"
Local $sLsrunase = @ScriptDir & "\提权程序\lsrunase.exe"

; 检查lsrunase是否存在
If Not FileExists($sLsrunase) Then
    MsgBox($MB_ICONERROR, "错误", "找不到lsrunase.exe文件: " & $sLsrunase)
    Exit
EndIf

; 让用户选择要测试的程序
Local $sTargetProgram = FileOpenDialog("选择要以管理员权限运行的程序", @WindowsDir, "可执行文件 (*.exe)", 1)
If @error Then Exit

; 获取程序目录
Local $sRunPath = StringRegExpReplace($sTargetProgram, "\\[^\\]*$", "")

; 构建lsrunase命令
Local $sCmd = '"' & $sLsrunase & '"'
$sCmd &= ' /user:' & $sUser
$sCmd &= ' /password:' & $sPassword
$sCmd &= ' /domain:' & $sDomain
$sCmd &= ' /command:"' & $sTargetProgram & '"'
$sCmd &= ' /runpath:"' & $sRunPath & '"'

; 显示将要执行的命令
MsgBox($MB_ICONINFORMATION, "测试信息", "将要执行的命令:" & @CRLF & $sCmd & @CRLF & @CRLF & "点击确定开始执行...")

; 执行命令
Local $iPID = Run($sCmd, $sRunPath, @SW_SHOW)

If $iPID = 0 Then
    MsgBox($MB_ICONERROR, "错误", "无法启动lsrunase程序")
Else
    MsgBox($MB_ICONINFORMATION, "成功", "lsrunase已启动，PID: " & $iPID & @CRLF & "正在以管理员权限运行目标程序...")
    
    ; 等待进程完成
    ProcessWaitClose($iPID)
    
    MsgBox($MB_ICONINFORMATION, "完成", "程序执行完成")
EndIf
