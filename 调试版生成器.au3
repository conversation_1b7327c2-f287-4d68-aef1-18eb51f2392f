#include <ButtonConstants.au3>
#include <EditConstants.au3>
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <FileConstants.au3>
#include <MsgBoxConstants.au3>

; 预设配置
Global Const $ADMIN_PASSWORD = "40lkg/kN/6Pm3ffhFw=="
Global Const $DOMAIN = "nvtpower.com"
Global Const $USERNAME = "administrator"

; GUI变量
Global $hGUI, $idFileSelect, $idTargetPath, $idGenerate, $idStatus, $idDebugMode
Global $sTargetFile = ""

; 创建主界面
_CreateGUI()

; 主循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE
            Exit
        Case $idFileSelect
            _SelectTargetFile()
        Case $idGenerate
            _GenerateElevatedExe()
    EndSwitch
WEnd

; 创建GUI界面
Func _CreateGUI()
    $hGUI = GUICreate("提权程序生成器 - 调试版", 500, 350, -1, -1)
    
    ; 标题
    GUICtrlCreateLabel("提权程序生成器 - 调试版", 20, 20, 460, 30, $SS_CENTER)
    GUICtrlSetFont(-1, 16, 600)
    
    ; 说明文字
    GUICtrlCreateLabel("选择需要以管理员权限运行的exe文件，生成新的提权版本", 20, 60, 460, 20, $SS_CENTER)
    
    ; 文件选择区域
    GUICtrlCreateGroup("目标文件选择", 20, 90, 460, 80)
    $idTargetPath = GUICtrlCreateEdit("", 40, 115, 320, 25, $ES_READONLY)
    $idFileSelect = GUICtrlCreateButton("选择文件", 370, 115, 80, 25)
    GUICtrlCreateGroup("", -99, -99, 1, 1)
    
    ; 调试选项
    GUICtrlCreateGroup("调试选项", 20, 180, 460, 50)
    $idDebugMode = GUICtrlCreateCheckbox("启用调试模式（显示详细信息）", 40, 200, 200, 20)
    GUICtrlSetState($idDebugMode, $GUI_CHECKED)
    GUICtrlCreateGroup("", -99, -99, 1, 1)
    
    ; 配置信息显示
    GUICtrlCreateGroup("配置信息", 20, 240, 460, 60)
    GUICtrlCreateLabel("用户名: " & $USERNAME, 40, 260, 200, 20)
    GUICtrlCreateLabel("域名: " & $DOMAIN, 250, 260, 200, 20)
    GUICtrlCreateLabel("密码: " & StringLeft($ADMIN_PASSWORD, 10) & "...", 40, 280, 200, 20)
    GUICtrlCreateGroup("", -99, -99, 1, 1)
    
    ; 生成按钮
    $idGenerate = GUICtrlCreateButton("生成提权程序", 200, 310, 100, 30)
    GUICtrlSetState($idGenerate, $GUI_DISABLE)
    
    ; 状态显示
    $idStatus = GUICtrlCreateLabel("请选择目标文件", 20, 345, 460, 15, $SS_CENTER)
    
    GUISetState(@SW_SHOW, $hGUI)
EndFunc

; 选择目标文件
Func _SelectTargetFile()
    Local $sFile = FileOpenDialog("选择要提权运行的exe文件", @DesktopDir, "可执行文件 (*.exe)", $FD_FILEMUSTEXIST)
    
    If @error Then Return
    
    $sTargetFile = $sFile
    GUICtrlSetData($idTargetPath, $sTargetFile)
    GUICtrlSetData($idStatus, "已选择: " & StringRegExpReplace($sTargetFile, ".*\\", ""))
    GUICtrlSetState($idGenerate, $GUI_ENABLE)
EndFunc

; 生成提权exe文件
Func _GenerateElevatedExe()
    If $sTargetFile = "" Then
        MsgBox($MB_ICONERROR, "错误", "请先选择目标文件")
        Return
    EndIf
    
    ; 检查必要文件是否存在
    If Not FileExists(@ScriptDir & "\提权程序\lsrunase.exe") Then
        MsgBox($MB_ICONERROR, "错误", "找不到lsrunase.exe文件，请确保文件位于程序目录的'提权程序'文件夹中")
        Return
    EndIf
    
    ; 获取保存路径
    Local $sSaveFile = FileSaveDialog("保存提权程序", @DesktopDir, "可执行文件 (*.exe)", $FD_PROMPTOVERWRITE, StringRegExpReplace($sTargetFile, ".*\\|\.exe$", "") & "_elevated.exe")
    
    If @error Then Return
    
    GUICtrlSetData($idStatus, "正在生成提权程序...")
    GUICtrlSetState($idGenerate, $GUI_DISABLE)
    
    ; 生成AutoIt脚本内容
    Local $sScript = _GenerateDebugScript()
    
    ; 创建临时脚本文件
    Local $sTempScript = @TempDir & "\temp_elevated_debug.au3"
    Local $hFile = FileOpen($sTempScript, $FO_OVERWRITE)
    
    If $hFile = -1 Then
        MsgBox($MB_ICONERROR, "错误", "无法创建临时脚本文件")
        GUICtrlSetState($idGenerate, $GUI_ENABLE)
        Return
    EndIf
    
    FileWrite($hFile, $sScript)
    FileClose($hFile)
    
    ; 显示生成的脚本内容（调试用）
    If GUICtrlRead($idDebugMode) = $GUI_CHECKED Then
        MsgBox($MB_ICONINFORMATION, "生成的脚本", "脚本已保存到: " & $sTempScript & @CRLF & "您可以查看脚本内容进行调试")
    EndIf
    
    ; 查找编译器
    Local $sCompilerPath = _FindAutoItCompiler()
    
    If $sCompilerPath = "" Then
        MsgBox($MB_ICONERROR, "错误", "找不到AutoIt编译器，请确保AutoIt已正确安装")
        FileDelete($sTempScript)
        GUICtrlSetState($idGenerate, $GUI_ENABLE)
        Return
    EndIf
    
    ; 执行编译
    Local $iPID = Run('"' & $sCompilerPath & '" /in "' & $sTempScript & '" /out "' & $sSaveFile & '"', "", @SW_HIDE)
    ProcessWaitClose($iPID)
    
    ; 检查是否生成成功
    If FileExists($sSaveFile) Then
        GUICtrlSetData($idStatus, "提权程序生成成功: " & StringRegExpReplace($sSaveFile, ".*\\", ""))
        MsgBox($MB_ICONINFORMATION, "成功", "提权程序已生成完成！" & @CRLF & "保存位置: " & $sSaveFile & @CRLF & @CRLF & "调试脚本保存在: " & $sTempScript)
    Else
        GUICtrlSetData($idStatus, "生成失败，请检查AutoIt编译器")
        MsgBox($MB_ICONERROR, "错误", "生成失败，请检查AutoIt编译器是否正确安装")
    EndIf
    
    GUICtrlSetState($idGenerate, $GUI_ENABLE)
EndFunc

; 生成调试版AutoIt脚本内容
Func _GenerateDebugScript()
    Local $sTargetFileName = StringRegExpReplace($sTargetFile, ".*\\", "")
    Local $sTargetDir = StringRegExpReplace($sTargetFile, "\\[^\\]*$", "")
    Local $bDebugMode = (GUICtrlRead($idDebugMode) = $GUI_CHECKED)
    
    Local $sScript = '#include <FileConstants.au3>' & @CRLF
    $sScript &= '#include <MsgBoxConstants.au3>' & @CRLF & @CRLF
    
    $sScript &= '; 提权程序自动生成脚本 - 调试版' & @CRLF
    $sScript &= '; 目标文件: ' & $sTargetFile & @CRLF
    $sScript &= '; 生成时间: ' & @YEAR & '-' & @MON & '-' & @MDAY & ' ' & @HOUR & ':' & @MIN & ':' & @SEC & @CRLF & @CRLF
    
    If $bDebugMode Then
        $sScript &= 'MsgBox($MB_ICONINFORMATION, "调试", "提权程序启动")' & @CRLF & @CRLF
    EndIf
    
    $sScript &= '; 定义文件路径' & @CRLF
    $sScript &= 'Local $sLsrunase = @TempDir & "\lsrunase.exe"' & @CRLF
    $sScript &= 'Local $sTargetExe = @TempDir & "\' & $sTargetFileName & '"' & @CRLF & @CRLF
    
    $sScript &= '; 解压lsrunase.exe' & @CRLF
    $sScript &= 'FileInstall("' & @ScriptDir & '\提权程序\lsrunase.exe", $sLsrunase, $FC_OVERWRITE)' & @CRLF
    $sScript &= 'If Not FileExists($sLsrunase) Then' & @CRLF
    $sScript &= '    MsgBox($MB_ICONERROR, "错误", "无法解压lsrunase.exe到: " & $sLsrunase)' & @CRLF
    $sScript &= '    Exit' & @CRLF
    $sScript &= 'EndIf' & @CRLF
    
    If $bDebugMode Then
        $sScript &= 'MsgBox($MB_ICONINFORMATION, "调试", "lsrunase.exe解压成功: " & $sLsrunase)' & @CRLF
    EndIf
    $sScript &= @CRLF
    
    $sScript &= '; 解压目标文件' & @CRLF
    $sScript &= 'FileInstall("' & $sTargetFile & '", $sTargetExe, $FC_OVERWRITE)' & @CRLF
    $sScript &= 'If Not FileExists($sTargetExe) Then' & @CRLF
    $sScript &= '    MsgBox($MB_ICONERROR, "错误", "无法解压目标文件到: " & $sTargetExe)' & @CRLF
    $sScript &= '    Exit' & @CRLF
    $sScript &= 'EndIf' & @CRLF
    
    If $bDebugMode Then
        $sScript &= 'MsgBox($MB_ICONINFORMATION, "调试", "目标文件解压成功: " & $sTargetExe)' & @CRLF
    EndIf
    $sScript &= @CRLF
    
    $sScript &= '; 配置参数' & @CRLF
    $sScript &= 'Local $sUser = "' & $USERNAME & '"' & @CRLF
    $sScript &= 'Local $sPassword = "' & $ADMIN_PASSWORD & '"' & @CRLF
    $sScript &= 'Local $sDomain = "' & $DOMAIN & '"' & @CRLF
    $sScript &= 'Local $sRunPath = "' & $sTargetDir & '"' & @CRLF & @CRLF
    
    $sScript &= '; 构建lsrunase命令' & @CRLF
    $sScript &= 'Local $sCmd = """" & $sLsrunase & """"' & @CRLF
    $sScript &= '$sCmd &= " /user:" & $sUser' & @CRLF
    $sScript &= '$sCmd &= " /password:" & $sPassword' & @CRLF
    $sScript &= '$sCmd &= " /domain:" & $sDomain' & @CRLF
    $sScript &= '$sCmd &= " /command:""" & $sTargetExe & """"' & @CRLF
    $sScript &= '$sCmd &= " /runpath:""" & $sRunPath & """"' & @CRLF & @CRLF
    
    If $bDebugMode Then
        $sScript &= 'MsgBox($MB_ICONINFORMATION, "调试", "执行命令: " & $sCmd)' & @CRLF & @CRLF
    EndIf
    
    $sScript &= '; 执行提权命令' & @CRLF
    $sScript &= 'Local $iPID = Run($sCmd, $sRunPath, @SW_SHOW)' & @CRLF
    $sScript &= 'If $iPID = 0 Then' & @CRLF
    $sScript &= '    MsgBox($MB_ICONERROR, "错误", "无法启动lsrunase程序，命令: " & $sCmd)' & @CRLF
    $sScript &= 'Else' & @CRLF
    
    If $bDebugMode Then
        $sScript &= '    MsgBox($MB_ICONINFORMATION, "调试", "lsrunase程序已启动，PID: " & $iPID)' & @CRLF
    EndIf
    
    $sScript &= '    ProcessWaitClose($iPID)' & @CRLF
    $sScript &= 'EndIf' & @CRLF & @CRLF
    
    $sScript &= '; 延迟后清理临时文件' & @CRLF
    $sScript &= 'Sleep(2000)' & @CRLF
    $sScript &= 'FileDelete($sLsrunase)' & @CRLF
    $sScript &= 'FileDelete($sTargetExe)' & @CRLF
    
    If $bDebugMode Then
        $sScript &= 'MsgBox($MB_ICONINFORMATION, "调试", "程序执行完成，临时文件已清理")' & @CRLF
    EndIf
    
    Return $sScript
EndFunc

; 查找AutoIt编译器
Func _FindAutoItCompiler()
    Local $aPaths[] = [ _
        "E:\AUTO\AutoIt3\Aut2Exe\Aut2exe.exe", _
        @ProgramFilesDir & "\AutoIt3\Aut2Exe\Aut2exe.exe", _
        @ProgramFilesDir & " (x86)\AutoIt3\Aut2Exe\Aut2exe.exe", _
        "C:\Program Files\AutoIt3\Aut2Exe\Aut2exe.exe", _
        "C:\Program Files (x86)\AutoIt3\Aut2Exe\Aut2exe.exe", _
        @ScriptDir & "\Aut2exe.exe" _
    ]
    
    For $i = 0 To UBound($aPaths) - 1
        If FileExists($aPaths[$i]) Then Return $aPaths[$i]
    Next
    
    Return ""
EndFunc
