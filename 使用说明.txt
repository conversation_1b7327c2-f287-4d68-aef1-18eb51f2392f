提权程序生成器 - 使用说明
================================

功能介绍：
本程序基于lsrunase工具，可以为任意exe文件生成具有管理员权限的提权版本。

使用步骤：
1. 确保程序目录下存在"提权程序"文件夹，其中包含lsrunase.exe文件
2. 运行"提权程序生成器.exe"
3. 点击"选择文件"按钮，选择需要提权运行的exe文件
4. 点击"生成提权程序"按钮
5. 选择保存位置和文件名
6. 等待生成完成

预设配置：
- 用户名: administrator
- 加密密码: 40lkg/kN/6Pm3ffhFw==
- 域名: nvtpower.com

注意事项：
1. 需要安装AutoIt才能编译生成exe文件
2. 生成的提权程序会在临时目录中解压并运行目标文件
3. 运行完成后会自动清理临时文件
4. 确保目标系统有administrator账户且密码正确

技术原理：
程序使用AutoIt的FileInstall功能将lsrunase.exe和目标文件嵌入到生成的exe中，
运行时自动解压到临时目录，然后调用lsrunase以管理员权限运行目标程序。

文件结构：
提权程序生成器.au3    - 主程序源码
提权程序/
  ├── lsrunase.exe     - 提权工具
  ├── LSencrypt.exe    - 密码加密工具
  └── 使用方法.txt     - lsrunase使用说明

编译方法：
使用AutoIt编译器将.au3文件编译为exe：
Aut2exe.exe /in 提权程序生成器.au3 /out 提权程序生成器.exe
