#include <ButtonConstants.au3>
#include <EditConstants.au3>
#include <GUIConstantsEx.au3>
#include <StaticConstants.au3>
#include <WindowsConstants.au3>
#include <FileConstants.au3>
#include <MsgBoxConstants.au3>

; 预设配置
Global Const $ADMIN_PASSWORD = "40lkg/kN/6Pm3ffhFw=="
Global Const $DOMAIN = "nvtpower.com"
Global Const $USERNAME = "administrator"

; GUI变量
Global $hGUI, $idFileSelect, $idTargetPath, $idGenerate, $idStatus, $idProgress
Global $sTargetFile = ""

; 验证必要文件
If Not _ValidateFiles() Then Exit

; 创建主界面
_CreateGUI()

; 主循环
While 1
    $nMsg = GUIGetMsg()
    Switch $nMsg
        Case $GUI_EVENT_CLOSE
            Exit
        Case $idFileSelect
            _SelectTargetFile()
        Case $idGenerate
            _GenerateElevatedExe()
    EndSwitch
WEnd

; 创建GUI界面
Func _CreateGUI()
    $hGUI = GUICreate("提权程序生成器", 500, 300, -1, -1)
    
    ; 标题
    GUICtrlCreateLabel("提权程序生成器", 20, 20, 460, 30, $SS_CENTER)
    GUICtrlSetFont(-1, 16, 600)
    
    ; 说明文字
    GUICtrlCreateLabel("选择需要以管理员权限运行的exe文件，生成新的提权版本", 20, 60, 460, 20, $SS_CENTER)
    
    ; 文件选择区域
    GUICtrlCreateGroup("目标文件选择", 20, 90, 460, 80)
    $idTargetPath = GUICtrlCreateEdit("", 40, 115, 320, 25, $ES_READONLY)
    $idFileSelect = GUICtrlCreateButton("选择文件", 370, 115, 80, 25)
    GUICtrlCreateGroup("", -99, -99, 1, 1)
    
    ; 配置信息显示
    GUICtrlCreateGroup("配置信息", 20, 180, 460, 60)
    GUICtrlCreateLabel("用户名: " & $USERNAME, 40, 200, 200, 20)
    GUICtrlCreateLabel("域名: " & $DOMAIN, 250, 200, 200, 20)
    GUICtrlCreateLabel("密码: " & StringLeft($ADMIN_PASSWORD, 10) & "...", 40, 220, 200, 20)
    GUICtrlCreateGroup("", -99, -99, 1, 1)
    
    ; 生成按钮
    $idGenerate = GUICtrlCreateButton("生成提权程序", 200, 250, 100, 30)
    GUICtrlSetState($idGenerate, $GUI_DISABLE)
    
    ; 状态显示
    $idStatus = GUICtrlCreateLabel("请选择目标文件", 20, 285, 460, 15, $SS_CENTER)
    
    GUISetState(@SW_SHOW, $hGUI)
EndFunc

; 选择目标文件
Func _SelectTargetFile()
    Local $sFile = FileOpenDialog("选择要提权运行的exe文件", @DesktopDir, "可执行文件 (*.exe)", $FD_FILEMUSTEXIST)
    
    If @error Then Return
    
    $sTargetFile = $sFile
    GUICtrlSetData($idTargetPath, $sTargetFile)
    GUICtrlSetData($idStatus, "已选择: " & StringRegExpReplace($sTargetFile, ".*\\", ""))
    GUICtrlSetState($idGenerate, $GUI_ENABLE)
EndFunc

; 生成提权exe文件
Func _GenerateElevatedExe()
    If $sTargetFile = "" Then
        MsgBox($MB_ICONERROR, "错误", "请先选择目标文件")
        Return
    EndIf
    
    ; 检查必要文件是否存在
    If Not FileExists(@ScriptDir & "\提权程序\lsrunase.exe") Then
        MsgBox($MB_ICONERROR, "错误", "找不到lsrunase.exe文件，请确保文件位于程序目录的'提权程序'文件夹中")
        Return
    EndIf
    
    ; 获取保存路径
    Local $sSaveFile = FileSaveDialog("保存提权程序", @DesktopDir, "可执行文件 (*.exe)", $FD_PROMPTOVERWRITE, StringRegExpReplace($sTargetFile, ".*\\|\.exe$", "") & "_elevated.exe")
    
    If @error Then Return
    
    GUICtrlSetData($idStatus, "正在生成提权程序...")
    GUICtrlSetState($idGenerate, $GUI_DISABLE)
    
    ; 生成AutoIt脚本内容
    Local $sScript = _GenerateScript()
    
    ; 创建临时脚本文件
    Local $sTempScript = @TempDir & "\temp_elevated.au3"
    Local $hFile = FileOpen($sTempScript, $FO_OVERWRITE)
    
    If $hFile = -1 Then
        MsgBox($MB_ICONERROR, "错误", "无法创建临时脚本文件")
        GUICtrlSetState($idGenerate, $GUI_ENABLE)
        Return
    EndIf
    
    FileWrite($hFile, $sScript)
    FileClose($hFile)
    
    ; 编译脚本为exe
    Local $sCompilerPath = _FindAutoItCompiler()
    
    If $sCompilerPath = "" Then
        MsgBox($MB_ICONERROR, "错误", "找不到AutoIt编译器，请确保AutoIt已正确安装")
        FileDelete($sTempScript)
        GUICtrlSetState($idGenerate, $GUI_ENABLE)
        Return
    EndIf
    
    ; 执行编译
    Local $iPID = Run('"' & $sCompilerPath & '" /in "' & $sTempScript & '" /out "' & $sSaveFile & '"', "", @SW_HIDE, $STDERR_CHILD + $STDOUT_CHILD)
    
    ProcessWaitClose($iPID)
    
    ; 清理临时文件
    FileDelete($sTempScript)
    
    ; 检查是否生成成功
    If FileExists($sSaveFile) Then
        GUICtrlSetData($idStatus, "提权程序生成成功: " & StringRegExpReplace($sSaveFile, ".*\\", ""))
        MsgBox($MB_ICONINFORMATION, "成功", "提权程序已生成完成！" & @CRLF & "保存位置: " & $sSaveFile)
    Else
        GUICtrlSetData($idStatus, "生成失败，请检查AutoIt编译器")
        MsgBox($MB_ICONERROR, "错误", "生成失败，请检查AutoIt编译器是否正确安装")
    EndIf
    
    GUICtrlSetState($idGenerate, $GUI_ENABLE)
EndFunc

; 生成AutoIt脚本内容
Func _GenerateScript()
    Local $sTargetFileName = StringRegExpReplace($sTargetFile, ".*\\", "")
    Local $sTargetDir = StringRegExpReplace($sTargetFile, "\\[^\\]*$", "")
    
    Local $sScript = '#include <FileConstants.au3>' & @CRLF
    $sScript &= '#include <MsgBoxConstants.au3>' & @CRLF & @CRLF
    
    $sScript &= '; 嵌入必要文件' & @CRLF
    $sScript &= 'FileInstall("' & @ScriptDir & '\提权程序\lsrunase.exe", @TempDir & "\lsrunase.exe", $FC_OVERWRITE)' & @CRLF
    $sScript &= 'FileInstall("' & $sTargetFile & '", @TempDir & "\' & $sTargetFileName & '", $FC_OVERWRITE)' & @CRLF & @CRLF
    
    $sScript &= '; 配置参数' & @CRLF
    $sScript &= 'Local $sUser = "' & $USERNAME & '"' & @CRLF
    $sScript &= 'Local $sPassword = "' & $ADMIN_PASSWORD & '"' & @CRLF
    $sScript &= 'Local $sDomain = "' & $DOMAIN & '"' & @CRLF
    $sScript &= 'Local $sCommand = @TempDir & "\' & $sTargetFileName & '"' & @CRLF
    $sScript &= 'Local $sRunPath = "' & $sTargetDir & '"' & @CRLF & @CRLF
    
    $sScript &= '; 构建lsrunase命令' & @CRLF
    $sScript &= 'Local $sCmd = @TempDir & "\lsrunase.exe"' & @CRLF
    $sScript &= '$sCmd &= " /user:" & $sUser' & @CRLF
    $sScript &= '$sCmd &= " /password:" & $sPassword' & @CRLF
    $sScript &= '$sCmd &= " /domain:" & $sDomain' & @CRLF
    $sScript &= '$sCmd &= " /command:" & $sCommand' & @CRLF
    $sScript &= '$sCmd &= " /runpath:" & $sRunPath' & @CRLF & @CRLF
    
    $sScript &= '; 执行提权命令' & @CRLF
    $sScript &= 'Local $iPID = Run($sCmd, "", @SW_HIDE)' & @CRLF
    $sScript &= 'ProcessWaitClose($iPID)' & @CRLF & @CRLF
    
    $sScript &= '; 清理临时文件' & @CRLF
    $sScript &= 'FileDelete(@TempDir & "\lsrunase.exe")' & @CRLF
    $sScript &= 'FileDelete(@TempDir & "\' & $sTargetFileName & '")' & @CRLF
    
    Return $sScript
EndFunc

; 查找AutoIt编译器
Func _FindAutoItCompiler()
    Local $aPaths[] = [ _
        @ProgramFilesDir & "\AutoIt3\Aut2Exe\Aut2exe.exe", _
        @ProgramFilesDir & " (x86)\AutoIt3\Aut2Exe\Aut2exe.exe", _
        "C:\Program Files\AutoIt3\Aut2Exe\Aut2exe.exe", _
        "C:\Program Files (x86)\AutoIt3\Aut2Exe\Aut2exe.exe", _
        @ScriptDir & "\Aut2exe.exe" _
    ]

    For $i = 0 To UBound($aPaths) - 1
        If FileExists($aPaths[$i]) Then Return $aPaths[$i]
    Next

    ; 尝试从注册表查找
    Local $sRegPath = RegRead("HKEY_LOCAL_MACHINE\SOFTWARE\AutoIt v3\AutoIt", "InstallDir")
    If Not @error And $sRegPath <> "" Then
        Local $sCompiler = $sRegPath & "\Aut2Exe\Aut2exe.exe"
        If FileExists($sCompiler) Then Return $sCompiler
    EndIf

    Return ""
EndFunc

; 验证文件完整性
Func _ValidateFiles()
    Local $aRequiredFiles[] = ["提权程序\lsrunase.exe"]

    For $i = 0 To UBound($aRequiredFiles) - 1
        If Not FileExists(@ScriptDir & "\" & $aRequiredFiles[$i]) Then
            MsgBox($MB_ICONERROR, "错误", "缺少必要文件: " & $aRequiredFiles[$i] & @CRLF & "请确保所有文件都在正确位置")
            Return False
        EndIf
    Next

    Return True
EndFunc
